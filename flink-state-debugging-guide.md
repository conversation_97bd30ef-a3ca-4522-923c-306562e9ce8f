# Flink State Disappearance Debugging Guide

## Overview

This guide describes how to debug Flink keyed state disappearance issues using the enhanced logging framework. The framework provides comprehensive, stateless logging that enables detection of state loss through log analysis patterns.

## Configuration

Enable debugging by configuring Log4j logging levels. Add the following to your `log4j2.properties` file:

```properties
# State disappearance logger configuration
logger.stateDisappearance.name=org.apache.flink.runtime.state.heap.StateDisappearanceLogger
logger.stateDisappearance.level=DEBUG
```

## Critical Alert Patterns

### 1. Snapshot Discrepancy Alerts

**Pattern**: `SNAPSHOT_VALIDATION.*discrepancies=.*[^empty]`

```
SNAPSHOT_VALIDATION correlationId=abc123 validationType=ENTRY_COUNT snapshotVersion=5 
expectedCount=1000 actualCount=998 discrepancies=Expected 1000 entries, found 998 in snapshot array
```

**Action**: Immediate investigation required - state entries are missing during snapshot creation.

### 2. Performance Overhead Alerts

**Pattern**: `DEBUG_PERFORMANCE_WARNING.*Logging overhead is high`

```
DEBUG_PERFORMANCE_WARNING: Logging overhead is high. Average time per log: 2500000 ns
```

**Action**: Consider reducing logging verbosity or increasing the slow threshold.

### 3. Race Condition Alerts

**Pattern**: `POTENTIAL_RACE_CONDITION`

```
POTENTIAL_RACE_CONDITION correlationId=def456 scenario=CONCURRENT_SNAPSHOT_UPDATE 
stateKey=user123:session mapVersion=10 entryVersion=8 snapshotVersion=9
```

**Action**: Indicates concurrent access during snapshot - investigate timing patterns.

## Log Analysis Patterns

### State Operation Tracking

**Pattern**: `STATE_OP correlationId=(\w+) operation=(\w+) stateKey=([^\\s]+)`

```
STATE_OP correlationId=xyz789 operation=PUT stateKey=user456:profile mapVersion=15 
entryVersion=12 requiredSnapshotVersion=10 isRehashing=false hasValue=true 
valueInfo=UserProfile@1234567 thread=Source-1 timestamp=1640995200000
```

**Analysis Points**:
- Track state key lifecycle (PUT → GET → REMOVE)
- Monitor version progression (mapVersion, entryVersion)
- Identify operations during rehashing (`isRehashing=true`)

### Snapshot Lifecycle Analysis

**Pattern**: `SNAPSHOT_LIFECYCLE correlationId=(\w+) event=(\w+) snapshotVersion=(\d+)`

```
SNAPSHOT_LIFECYCLE correlationId=abc123 event=CREATE snapshotVersion=5 checkpointId=12345 
mapVersion=4 entryCount=1000 activeSnapshots=2 thread=Checkpoint-1 timestamp=1640995200000
```

**Key Events**:
- `CREATE` - Snapshot creation started
- `CREATED` - Snapshot creation completed
- `RELEASE` - Snapshot released

### User Function Access Patterns

**Pattern**: `USER_FUNCTION_ACCESS correlationId=(\w+) operation=(\w+) stateName=([^\\s]+)`

```
USER_FUNCTION_ACCESS correlationId=def456 operation=GET stateName=userState 
functionClass=com.example.MyProcessFunction thread=Source-1 timestamp=1640995200000
```

**Analysis Points**:
- Identify which user functions access state
- Track access patterns and timing
- Detect unusual access during checkpoints

### Version Tracking

**Pattern**: `VERSION_OP correlationId=(\w+) operation=(\w+)`

```
VERSION_OP correlationId=ghi789 operation=INCREMENT_MAP_VERSION oldVersion=10 
newVersion=11 reason=COPY_ON_WRITE activeSnapshots=1 thread=Source-1 timestamp=1640995200000
```

**Critical Scenarios**:
- Version increments during active snapshots
- Unexpected version jumps
- Version rollbacks (should not happen)

## Debugging Workflows

### 1. State Loss Investigation

1. **Search for missing state key**:
   ```bash
   grep "stateKey=user123:session" flink.log | sort -k timestamp
   ```

2. **Check last known operations**:
   - Look for final `PUT` or `REMOVE` operations
   - Verify no explicit removal by user code

3. **Examine concurrent snapshot activity**:
   ```bash
   grep -E "(SNAPSHOT_|STATE_OP).*user123:session" flink.log | sort -k timestamp
   ```

4. **Look for race condition indicators**:
   ```bash
   grep "POTENTIAL_RACE_CONDITION.*user123:session" flink.log
   ```

### 2. Performance Analysis

1. **Monitor logging overhead**:
   ```bash
   grep "DEBUG_PERFORMANCE" flink.log | tail -20
   ```

2. **Check slow operations**:
   ```bash
   grep "STATE_ACCESS_TIMING.*durationNanos=[0-9]{7,}" flink.log
   ```

3. **Analyze thread-specific performance**:
   ```bash
   grep "DEBUG_PERFORMANCE_STATS.*thread=Source-1" flink.log
   ```

### 3. Snapshot Integrity Verification

1. **Check for snapshot discrepancies**:
   ```bash
   grep "SNAPSHOT_VALIDATION.*actualCount.*expectedCount" flink.log | \
   awk '$6 != $7 {print}'
   ```

2. **Analyze snapshot iteration patterns**:
   ```bash
   grep "SNAPSHOT_ITERATION" flink.log | grep "snapshotVersion=5"
   ```

3. **Track snapshot-state interactions**:
   ```bash
   grep "SNAPSHOT_STATE_INTERACTION" flink.log | sort -k timestamp
   ```

## TaskManager Checkpoint Lifecycle Logging

### Enabling Checkpoint Lifecycle Logging

To enable comprehensive checkpoint lifecycle logging at the TaskManager level, configure the following system properties and logging levels:

#### Log4j Configuration for Checkpoint Lifecycle Logging

Add the following configuration to your `log4j2.properties` file:

```properties
# Checkpoint Coordinator
logger.checkpointCoordinator.name=org.apache.flink.runtime.checkpoint.CheckpointCoordinator
logger.checkpointCoordinator.level=DEBUG
logger.checkpointCoordinator.additivity=false
logger.checkpointCoordinator.appenderRef.checkpointFile.ref=CheckpointFileAppender

# Checkpoint Barrier Handling
logger.checkpointBarrierHandler.name=org.apache.flink.streaming.runtime.io.CheckpointBarrierHandler
logger.checkpointBarrierHandler.level=DEBUG
logger.checkpointBarrierHandler.additivity=false
logger.checkpointBarrierHandler.appenderRef.checkpointFile.ref=CheckpointFileAppender

logger.checkpointBarrierAligner.name=org.apache.flink.streaming.runtime.io.CheckpointBarrierAligner
logger.checkpointBarrierAligner.level=DEBUG
logger.checkpointBarrierAligner.additivity=false
logger.checkpointBarrierAligner.appenderRef.checkpointFile.ref=CheckpointFileAppender

# State Backend Checkpointing
logger.runtimeState.name=org.apache.flink.runtime.state
logger.runtimeState.level=DEBUG
logger.runtimeState.additivity=false
logger.runtimeState.appenderRef.checkpointFile.ref=CheckpointFileAppender

logger.heapState.name=org.apache.flink.runtime.state.heap
logger.heapState.level=DEBUG
logger.heapState.additivity=false
logger.heapState.appenderRef.checkpointFile.ref=CheckpointFileAppender

logger.filesystemState.name=org.apache.flink.runtime.state.filesystem
logger.filesystemState.level=DEBUG
logger.filesystemState.additivity=false
logger.filesystemState.appenderRef.checkpointFile.ref=CheckpointFileAppender

# Checkpoint Statistics
logger.checkpointStats.name=org.apache.flink.runtime.checkpoint.CheckpointStatsTracker
logger.checkpointStats.level=INFO
logger.checkpointStats.additivity=false
logger.checkpointStats.appenderRef.checkpointFile.ref=CheckpointFileAppender

# Task Checkpoint Execution
logger.streamTask.name=org.apache.flink.streaming.runtime.tasks.StreamTask
logger.streamTask.level=DEBUG
logger.streamTask.additivity=false
logger.streamTask.appenderRef.checkpointFile.ref=CheckpointFileAppender

logger.abstractStreamOperator.name=org.apache.flink.streaming.api.operators.AbstractStreamOperator
logger.abstractStreamOperator.level=DEBUG
logger.abstractStreamOperator.additivity=false
logger.abstractStreamOperator.appenderRef.checkpointFile.ref=CheckpointFileAppender

# Checkpoint Storage
logger.stateStorage.name=org.apache.flink.runtime.state.storage
logger.stateStorage.level=DEBUG
logger.stateStorage.additivity=false
logger.stateStorage.appenderRef.checkpointFile.ref=CheckpointFileAppender

logger.checkpointStorageLocation.name=org.apache.flink.runtime.state.CheckpointStorageLocation
logger.checkpointStorageLocation.level=DEBUG
logger.checkpointStorageLocation.additivity=false
logger.checkpointStorageLocation.appenderRef.checkpointFile.ref=CheckpointFileAppender

# Checkpoint-specific file appender
appender.checkpointFile.type=RollingFile
appender.checkpointFile.name=CheckpointFileAppender
appender.checkpointFile.fileName=logs/flink-checkpoint-debug.log
appender.checkpointFile.filePattern=logs/flink-checkpoint-debug.%d{yyyy-MM-dd}.%i.gz
appender.checkpointFile.layout.type=PatternLayout
appender.checkpointFile.layout.pattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%t] %c{1} - %m%n
appender.checkpointFile.policies.type=Policies
appender.checkpointFile.policies.time.type=TimeBasedTriggeringPolicy
appender.checkpointFile.policies.size.type=SizeBasedTriggeringPolicy
appender.checkpointFile.policies.size.size=100MB
appender.checkpointFile.strategy.type=DefaultRolloverStrategy
appender.checkpointFile.strategy.max=10
```

#### Flink Configuration (flink-conf.yaml)

```yaml
# Enable checkpoint statistics
jobmanager.execution.failover-strategy: region
state.checkpoints.dir: file:///path/to/checkpoints
state.savepoints.dir: file:///path/to/savepoints

# Checkpoint configuration for debugging
execution.checkpointing.interval: 30s
execution.checkpointing.timeout: 10min
execution.checkpointing.min-pause: 5s
execution.checkpointing.max-concurrent-checkpoints: 1

# Enable detailed metrics
metrics.reporters: slf4j
metrics.reporter.slf4j.class: org.apache.flink.metrics.slf4j.Slf4jReporter
metrics.reporter.slf4j.interval: 10 SECONDS
```

### Key Checkpoint Lifecycle Log Patterns

#### Checkpoint Initiation
```
CheckpointCoordinator - Triggering checkpoint 12345 at 1640995200000
StreamTask - Starting checkpoint 12345 for task Source-1 (1/4)
```

#### Barrier Processing
```
CheckpointBarrierHandler - Received checkpoint barrier for checkpoint 12345 from channel 0
CheckpointBarrierAligner - Aligning checkpoint 12345, blocked channels: [1, 2]
```

#### State Snapshotting
```
AbstractStreamOperator - Creating snapshot for checkpoint 12345
HeapKeyedStateBackend - Starting async snapshot for checkpoint 12345
StateDisappearanceLogger - SNAPSHOT_LIFECYCLE event=CREATE snapshotVersion=5 checkpointId=12345
```

#### Checkpoint Completion
```
CheckpointCoordinator - Completed checkpoint 12345 in 2.5s (size: 1.2 GB)
StreamTask - Finished checkpoint 12345 for task Source-1 (1/4)
```

### Monitoring Checkpoint Health

#### 1. Real-time Checkpoint Monitoring Script

```bash
#!/bin/bash
# checkpoint-monitor.sh

tail -f flink-taskmanager.log | while read line; do
    # Monitor checkpoint start
    if echo "$line" | grep -q "Triggering checkpoint"; then
        checkpoint_id=$(echo "$line" | grep -o 'checkpoint [0-9]*' | awk '{print $2}')
        echo "CHECKPOINT_START: $checkpoint_id at $(date)"
    fi

    # Monitor checkpoint completion
    if echo "$line" | grep -q "Completed checkpoint"; then
        checkpoint_info=$(echo "$line" | grep -o 'checkpoint [0-9]* in [0-9.]*s')
        echo "CHECKPOINT_COMPLETE: $checkpoint_info"
    fi

    # Monitor checkpoint failures
    if echo "$line" | grep -q "Checkpoint.*failed"; then
        echo "CHECKPOINT_FAILED: $line"
    fi

    # Monitor barrier alignment issues
    if echo "$line" | grep -q "Aligning checkpoint.*blocked"; then
        echo "BARRIER_ALIGNMENT: $line"
    fi
done
```

#### 2. Checkpoint Performance Analysis

```bash
# Extract checkpoint durations
grep "Completed checkpoint" flink-taskmanager.log | \
awk '{print $NF}' | sed 's/[^0-9.]//g' | \
awk '{sum+=$1; count++} END {print "Avg:", sum/count "s", "Count:", count}'

# Find slow checkpoints (>5 seconds)
grep "Completed checkpoint" flink-taskmanager.log | \
awk '$NF ~ /[5-9][0-9]*\.[0-9]*s|[0-9][0-9]+\.[0-9]*s/ {print}'

# Monitor checkpoint failures
grep -E "(failed|timeout|abort)" flink-taskmanager.log | grep -i checkpoint
```

#### 3. TaskManager-Specific Checkpoint Debugging

```bash
# Monitor checkpoint barriers across all input channels
grep "CheckpointBarrierHandler" flink-taskmanager.log | \
grep "checkpoint 12345" | sort -k timestamp

# Track state backend snapshot operations
grep -E "(HeapKeyedStateBackend|RocksDBKeyedStateBackend)" flink-taskmanager.log | \
grep "snapshot" | grep "checkpoint 12345"

# Monitor checkpoint storage operations
grep "CheckpointStorageLocation" flink-taskmanager.log | \
grep "checkpoint 12345"
```

### Integration with State Disappearance Logging

Combine TaskManager checkpoint logs with state disappearance logs for comprehensive analysis:

```bash
# Correlate checkpoint events with state operations
grep -E "(CHECKPOINT_|STATE_OP|SNAPSHOT_)" flink.log | \
grep "checkpointId=12345\|checkpoint 12345" | sort -k timestamp

# Find state operations during checkpoint barriers
grep -A 10 -B 10 "CheckpointBarrierHandler.*checkpoint 12345" flink.log | \
grep "STATE_OP"
```

## Advanced Analysis Patterns

### Rehashing Operation Analysis

**Pattern**: `REHASHING_OP correlationId=(\w+) operation=(\w+)`

```
REHASHING_OP correlationId=jkl012 operation=INCREMENTAL_STEP oldTableSize=1024
newTableSize=2048 rehashIndex=512 entriesMoved=256 thread=Source-1 timestamp=1640995200000
```

**Critical Scenarios**:
- State access during rehashing (`isRehashing=true` in STATE_OP logs)
- Rehashing during snapshot creation
- Incomplete rehashing operations

### Copy-on-Write Analysis

**Pattern**: `COPY_ON_WRITE correlationId=(\w+) trigger=(\w+)`

```
COPY_ON_WRITE correlationId=mno345 trigger=SNAPSHOT_ACTIVE stateKey=user789:data
oldVersion=5 newVersion=6 snapshotVersion=5 thread=Source-1 timestamp=1640995200000
```

**Analysis Points**:
- Frequency of copy-on-write operations
- Correlation with snapshot activity
- Version progression consistency

### State Consistency Validation

**Pattern**: `STATE_CONSISTENCY correlationId=(\w+) validation=(\w+)`

```
STATE_CONSISTENCY correlationId=pqr678 validation=VERSION_CHECK stateKey=user456:session
expectedVersion=10 actualVersion=8 status=MISMATCH thread=Source-1 timestamp=1640995200000
```

**Red Flags**:
- Version mismatches
- Null values when expecting data
- Inconsistent state sizes

## Troubleshooting Scenarios

### Scenario 1: State Disappears During Checkpoint

**Symptoms**:
- State exists before checkpoint
- State missing after checkpoint
- No explicit REMOVE operations in logs

**Investigation Steps**:

1. **Identify checkpoint timing**:
   ```bash
   grep "CHECKPOINT_START\|CHECKPOINT_COMPLETE" flink.log | grep "checkpointId=12345"
   ```

2. **Find state operations during checkpoint**:
   ```bash
   grep -E "STATE_OP.*stateKey=user123:session" flink.log | \
   awk '/timestamp=1640995200000/,/timestamp=1640995300000/'
   ```

3. **Check for snapshot discrepancies**:
   ```bash
   grep "SNAPSHOT_VALIDATION.*snapshotVersion=5" flink.log
   ```

4. **Look for concurrent operations**:
   ```bash
   grep -E "(SNAPSHOT_|REHASHING_|COPY_ON_WRITE).*timestamp=164099520" flink.log | sort
   ```

### Scenario 2: Performance Degradation

**Symptoms**:
- Increasing checkpoint duration
- High logging overhead warnings
- Slow state operations

**Investigation Steps**:

1. **Check logging performance**:
   ```bash
   grep "DEBUG_PERFORMANCE_STATS" flink.log | tail -10
   ```

2. **Identify slow operations**:
   ```bash
   grep "STATE_ACCESS_TIMING.*durationNanos=[0-9]{7,}" flink.log | \
   sort -k durationNanos -nr | head -20
   ```

3. **Analyze operation frequency**:
   ```bash
   grep "STATE_OP" flink.log | awk '{print $3}' | sort | uniq -c | sort -nr
   ```

### Scenario 3: Race Condition Detection

**Symptoms**:
- Intermittent state loss
- POTENTIAL_RACE_CONDITION logs
- Version inconsistencies

**Investigation Steps**:

1. **Find race condition patterns**:
   ```bash
   grep "POTENTIAL_RACE_CONDITION" flink.log | \
   awk '{print $4, $5}' | sort | uniq -c
   ```

2. **Analyze concurrent access patterns**:
   ```bash
   grep -E "STATE_OP.*stateKey=user123:session" flink.log | \
   awk '{print $NF, $(NF-1)}' | sort
   ```

3. **Check version progression**:
   ```bash
   grep -E "VERSION_OP.*INCREMENT" flink.log | \
   awk '{print $6, $7}' | sort -n
   ```

## Monitoring and Alerting

### Log Analysis Queries

**Find all operations for a specific state key**:
```bash
grep "stateKey=user123:session" flink.log | \
awk '{print $NF, $3, $4}' | sort -k1,1n
```

**Analyze checkpoint impact on state operations**:
```bash
join -t' ' -1 1 -2 1 \
  <(grep "CHECKPOINT_START" flink.log | awk '{print $3, $1}' | sort) \
  <(grep "STATE_OP" flink.log | awk '{print $NF, $0}' | sort) | \
head -50
```

**Track state lifecycle for debugging**:
```bash
grep -E "(STATE_OP|USER_FUNCTION_ACCESS).*stateKey=user123:session" flink.log | \
sort -k timestamp | \
awk '{print $NF, $3, $4, $5}' | \
column -t
```

This comprehensive logging framework enables thorough investigation of state disappearance issues through structured log analysis, providing the visibility needed to identify and resolve complex race conditions in Flink's state backend.
