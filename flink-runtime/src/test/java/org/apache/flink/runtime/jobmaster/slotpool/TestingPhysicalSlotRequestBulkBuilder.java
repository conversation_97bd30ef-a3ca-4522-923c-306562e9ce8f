/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.jobmaster.slotpool;

import org.apache.flink.runtime.clusterframework.types.ResourceProfile;
import org.apache.flink.runtime.jobmaster.SlotRequestId;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

class TestingPhysicalSlotRequestBulkBuilder {
    private static final BiConsumer<SlotRequestId, Throwable> EMPTY_CANCELLER = (r, t) -> {};
    private Map<SlotRequestId, ResourceProfile> pendingRequests = new HashMap<>();
    private BiConsumer<SlotRequestId, Throwable> canceller = EMPTY_CANCELLER;

    TestingPhysicalSlotRequestBulkBuilder addPendingRequest(
            SlotRequestId slotRequestId, ResourceProfile resourceProfile) {
        pendingRequests.put(slotRequestId, resourceProfile);
        return this;
    }

    TestingPhysicalSlotRequestBulkBuilder setCanceller(
            BiConsumer<SlotRequestId, Throwable> canceller) {
        this.canceller = canceller;
        return this;
    }

    PhysicalSlotRequestBulkImpl buildPhysicalSlotRequestBulkImpl() {
        return new PhysicalSlotRequestBulkImpl(pendingRequests, canceller);
    }

    PhysicalSlotRequestBulkWithTimestamp buildPhysicalSlotRequestBulkWithTimestamp() {
        return new PhysicalSlotRequestBulkWithTimestamp(buildPhysicalSlotRequestBulkImpl());
    }

    static TestingPhysicalSlotRequestBulkBuilder newBuilder() {
        return new TestingPhysicalSlotRequestBulkBuilder();
    }
}
