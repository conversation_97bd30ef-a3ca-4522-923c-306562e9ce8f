/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.state.heap;

import org.apache.flink.api.common.state.State;
import org.apache.flink.api.common.state.StateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.runtime.state.internal.InternalValueState;

/**
 * Heap-backed partitioned {@link ValueState} that is snapshotted into files.
 *
 * @param <K> The type of the key.
 * @param <N> The type of the namespace.
 * @param <V> The type of the value.
 */
class HeapValueState<K, N, V> extends AbstractHeapState<K, N, V>
        implements InternalValueState<K, N, V> {

    /**
     * Creates a new key/value state for the given hash map of key/value pairs.
     *
     * @param stateTable The state table for which this state is associated to.
     * @param keySerializer The serializer for the keys.
     * @param valueSerializer The serializer for the state.
     * @param namespaceSerializer The serializer for the namespace.
     * @param defaultValue The default value for the state.
     */
    private HeapValueState(
            StateTable<K, N, V> stateTable,
            TypeSerializer<K> keySerializer,
            TypeSerializer<V> valueSerializer,
            TypeSerializer<N> namespaceSerializer,
            V defaultValue) {
        super(stateTable, keySerializer, valueSerializer, namespaceSerializer, defaultValue);
    }

    @Override
    public TypeSerializer<K> getKeySerializer() {
        return keySerializer;
    }

    @Override
    public TypeSerializer<N> getNamespaceSerializer() {
        return namespaceSerializer;
    }

    @Override
    public TypeSerializer<V> getValueSerializer() {
        return valueSerializer;
    }

    @Override
    public V value() {
        long startTime = System.nanoTime();

        // Log user function access
        StateDisappearanceLogger.logUserFunctionAccess("GET", "ValueState",
            stateDesc.getName(), getUserFunctionClass(), "value()");

        final V result = stateTable.get(currentNamespace);

        long duration = System.nanoTime() - startTime;
        StateDisappearanceLogger.logStateAccessTiming("GET", stateDesc.getName(),
            duration, getUserFunctionClass());

        if (result == null) {
            return getDefaultValue();
        }

        return result;
    }

    @Override
    public void update(V value) {
        long startTime = System.nanoTime();

        // Log user function access
        StateDisappearanceLogger.logUserFunctionAccess("UPDATE", "ValueState",
            stateDesc.getName(), getUserFunctionClass(), "update()");

        if (value == null) {
            StateDisappearanceLogger.logUserFunctionAccess("CLEAR", "ValueState",
                stateDesc.getName(), getUserFunctionClass(), "update(null)");
            clear();
            return;
        }

        stateTable.put(currentNamespace, value);

        long duration = System.nanoTime() - startTime;
        StateDisappearanceLogger.logStateAccessTiming("UPDATE", stateDesc.getName(),
            duration, getUserFunctionClass());
    }

    /**
     * Helper method to get the user function class name from the call stack.
     * This attempts to identify the user function that is accessing state.
     */
    private String getUserFunctionClass() {
        StackTraceElement[] stack = Thread.currentThread().getStackTrace();

        // Look for user function classes in the stack trace
        for (StackTraceElement element : stack) {
            String className = element.getClassName();

            // Skip Flink internal classes and common Java classes
            if (className.startsWith("cellutions.seenow".)) {
                return className;
            }
        }

        return "UNKNOWN_USER_FUNCTION";
    }

    @SuppressWarnings("unchecked")
    static <K, N, SV, S extends State, IS extends S> IS create(
            StateDescriptor<S, SV> stateDesc,
            StateTable<K, N, SV> stateTable,
            TypeSerializer<K> keySerializer) {
        return (IS)
                new HeapValueState<>(
                        stateTable,
                        keySerializer,
                        stateTable.getStateSerializer(),
                        stateTable.getNamespaceSerializer(),
                        stateDesc.getDefaultValue());
    }

    @SuppressWarnings("unchecked")
    static <K, N, SV, S extends State, IS extends S> IS update(
            StateDescriptor<S, SV> stateDesc, StateTable<K, N, SV> stateTable, IS existingState) {
        return (IS)
                ((HeapValueState<K, N, SV>) existingState)
                        .setNamespaceSerializer(stateTable.getNamespaceSerializer())
                        .setValueSerializer(stateTable.getStateSerializer())
                        .setDefaultValue(stateDesc.getDefaultValue());
    }
}
