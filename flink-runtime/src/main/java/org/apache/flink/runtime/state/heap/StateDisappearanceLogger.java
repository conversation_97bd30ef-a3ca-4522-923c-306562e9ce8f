/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.state.heap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Stateless logger for debugging state disappearance issues in CopyOnWriteStateMap.
 *
 * This logger is designed to be completely stateless - all information needed to diagnose
 * state disappearance is embedded in the log messages themselves. This approach ensures
 * that state loss can be analyzed purely from log analysis without maintaining any
 * in-memory state that would be lost on job restarts.
 *
 * Log messages are structured to enable correlation analysis:
 * - Each operation includes a correlation ID for tracking related operations
 * - State keys are consistently formatted for easy grep/analysis
 * - Version information is included to detect race conditions
 * - Timing information enables detection of concurrent operations
 *
 * To analyze state disappearance:
 * 1. Search for STATE_OP logs with operation=PUT for a specific stateKey
 * 2. Search for STATE_RETRIEVAL logs with result=null for the same stateKey
 * 3. Look for SNAPSHOT_OP and REHASH_OP logs between PUT and failed retrieval
 * 4. Check for COW_OP logs indicating copy-on-write operations
 * 5. Analyze version numbers and timestamps to identify race conditions
 */
public class StateDisappearanceLogger {

    private static final Logger LOG = LoggerFactory.getLogger(StateDisappearanceLogger.class);

    // Performance monitoring thresholds (in nanoseconds)
    private static final long SLOW_OPERATION_THRESHOLD_NANOS = 1000000L; // 1ms default

    // Thread-local counters for performance monitoring
    private static final ThreadLocal<Long> THREAD_LOG_COUNT = ThreadLocal.withInitial(() -> 0L);
    private static final ThreadLocal<Long> THREAD_LOG_TIME_NANOS = ThreadLocal.withInitial(() -> 0L);

    // Static initialization flag
    private static volatile boolean configurationLogged = false;

    // Thread-local operation counter for correlation within a single thread
    private static final ThreadLocal<Long> threadOperationCounter =
        ThreadLocal.withInitial(() -> 0L);

    /**
     * Gets the next operation ID for the current thread to enable correlation of related operations.
     */
    private static long getNextThreadOperationId() {
        long current = threadOperationCounter.get();
        threadOperationCounter.set(current + 1);
        return current;
    }

    /**
     * Creates a unique correlation ID that includes thread info and timestamp for cross-thread correlation.
     */
    private static String createCorrelationId() {
        return String.format("%s-%d-%d",
            Thread.currentThread().getName(),
            System.currentTimeMillis(),
            getNextThreadOperationId());
    }

    /**
     * Creates a deterministic key identifier for state entries that can be used for log correlation.
     */
    private static String createStateKey(Object key, Object namespace) {
        return String.format("K[%s]N[%s]",
            key != null ? key.toString() : "null",
            namespace != null ? namespace.toString() : "null");
    }

    /**
     * Logs state operations (PUT, REMOVE, TRANSFORM) with all context needed for analysis.
     *
     * Log format: STATE_OP correlationId=X operation=Y stateKey=Z mapVersion=A entryVersion=B
     *            requiredSnapshotVersion=C isRehashing=D hasValue=E thread=F timestamp=G
     */
    public static void logStateOperation(String operation, Object key, Object namespace,
                                       Object value, int mapVersion, int entryVersion,
                                       int highestRequiredSnapshotVersion, boolean isRehashing) {
        if (!LOG.isDebugEnabled()) return;

        ensureConfigurationLogged();
        logWithPerformanceTracking(() -> {
            String correlationId = createCorrelationId();
            String stateKey = createStateKey(key, namespace);
            String thread = Thread.currentThread().getName();
            long timestamp = System.currentTimeMillis();
            boolean hasValue = (value != null);
            String valueInfo = hasValue ? value.getClass().getSimpleName() + "@" + System.identityHashCode(value) : "null";

            LOG.debug("STATE_OP correlationId={} operation={} stateKey={} mapVersion={} entryVersion={} " +
                    "requiredSnapshotVersion={} isRehashing={} hasValue={} valueInfo={} thread={} timestamp={}",
                    correlationId, operation, stateKey, mapVersion, entryVersion,
                    highestRequiredSnapshotVersion, isRehashing, hasValue, valueInfo, thread, timestamp);
        });
    }

    /**
     * Logs state retrieval operations with context for detecting state disappearance.
     *
     * Log format: STATE_RETRIEVAL correlationId=X stateKey=Y result=Z mapVersion=A
     *            requiredSnapshotVersion=B isRehashing=C tableUsed=D thread=E timestamp=F
     */
    public static void logStateRetrieval(Object key, Object namespace, Object retrievedValue,
                                       int mapVersion, int highestRequiredSnapshotVersion,
                                       boolean isRehashing, String tableUsed) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String stateKey = createStateKey(key, namespace);
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();
        String result = (retrievedValue != null) ? "found" : "null";
        String valueInfo = (retrievedValue != null) ?
            retrievedValue.getClass().getSimpleName() + "@" + System.identityHashCode(retrievedValue) : "null";

        LOG.debug("STATE_RETRIEVAL correlationId={} stateKey={} result={} valueInfo={} mapVersion={} " +
                "requiredSnapshotVersion={} isRehashing={} tableUsed={} thread={} timestamp={}",
                correlationId, stateKey, result, valueInfo, mapVersion,
                highestRequiredSnapshotVersion, isRehashing, tableUsed, thread, timestamp);
    }

    /**
     * Logs snapshot operations for tracking snapshot lifecycle and potential race conditions.
     *
     * Log format: SNAPSHOT_OP correlationId=X operation=Y snapshotVersion=Z currentMapVersion=A
     *            entryCount=B isRehashing=C thread=D timestamp=E
     */
    public static void logSnapshotOperation(String operation, int snapshotVersion,
                                          int currentMapVersion, int entryCount, boolean isRehashing) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("SNAPSHOT_OP correlationId={} operation={} snapshotVersion={} currentMapVersion={} " +
                "entryCount={} isRehashing={} thread={} timestamp={}",
                correlationId, operation, snapshotVersion, currentMapVersion,
                entryCount, isRehashing, thread, timestamp);
    }

    /**
     * Logs incremental rehashing operations for detecting rehashing-related race conditions.
     *
     * Log format: REHASH_OP correlationId=X operation=Y rehashIndex=Z oldCapacity=A newCapacity=B
     *            transferredEntries=C mapVersion=D requiredSnapshotVersion=E thread=F timestamp=G
     */
    public static void logRehashingOperation(String operation, int rehashIndex, int oldCapacity,
                                           int newCapacity, int transferredEntries,
                                           int mapVersion, int requiredSnapshotVersion) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("REHASH_OP correlationId={} operation={} rehashIndex={} oldCapacity={} newCapacity={} " +
                "transferredEntries={} mapVersion={} requiredSnapshotVersion={} thread={} timestamp={}",
                correlationId, operation, rehashIndex, oldCapacity, newCapacity,
                transferredEntries, mapVersion, requiredSnapshotVersion, thread, timestamp);
    }

    /**
     * Logs version-related operations for tracking version consistency.
     *
     * Log format: VERSION_OP correlationId=X operation=Y oldVersion=Z newVersion=A
     *            requiredVersion=B context=C thread=D timestamp=E
     */
    public static void logVersionOperation(String operation, int oldVersion, int newVersion,
                                         int requiredVersion, String context) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("VERSION_OP correlationId={} operation={} oldVersion={} newVersion={} " +
                "requiredVersion={} context={} thread={} timestamp={}",
                correlationId, operation, oldVersion, newVersion, requiredVersion,
                context, thread, timestamp);
    }

    /**
     * Logs copy-on-write operations for debugging COW-related issues.
     *
     * Log format: COW_OP correlationId=X operation=Y stateKey=Z entryVersion=A stateVersion=B
     *            requiredSnapshotVersion=C context=D thread=E timestamp=F
     */
    public static void logCopyOnWriteOperation(String operation, Object key, Object namespace,
                                             int entryVersion, int stateVersion,
                                             int requiredSnapshotVersion, String context) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String stateKey = createStateKey(key, namespace);
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("COW_OP correlationId={} operation={} stateKey={} entryVersion={} stateVersion={} " +
                "requiredSnapshotVersion={} context={} thread={} timestamp={}",
                correlationId, operation, stateKey, entryVersion, stateVersion,
                requiredSnapshotVersion, context, thread, timestamp);
    }

    /**
     * Logs potential race condition scenarios for immediate attention.
     *
     * Log format: RACE_CONDITION correlationId=X scenario=Y stateKey=Z details=A thread=B timestamp=C
     */
    public static void logPotentialRaceCondition(String scenario, Object key, Object namespace,
                                                String details) {
        String correlationId = createCorrelationId();
        String stateKey = createStateKey(key, namespace);
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.warn("RACE_CONDITION correlationId={} scenario={} stateKey={} details={} thread={} timestamp={}",
                correlationId, scenario, stateKey, details, thread, timestamp);
    }

    /**
     * Validates state consistency by logging expected vs actual state.
     * This method doesn't maintain state but logs information that can be used
     * for post-hoc analysis of state consistency.
     *
     * Log format: STATE_CONSISTENCY correlationId=X stateKey=Y actualExists=Z
     *            context=A thread=B timestamp=C
     */
    public static void validateStateConsistency(Object key, Object namespace, Object actualValue,
                                              String context) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String stateKey = createStateKey(key, namespace);
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();
        boolean actualExists = (actualValue != null);

        LOG.debug("STATE_CONSISTENCY correlationId={} stateKey={} actualExists={} context={} thread={} timestamp={}",
                correlationId, stateKey, actualExists, context, thread, timestamp);
    }

    /**
     * Logs user function state access patterns for detecting problematic usage.
     *
     * Log format: USER_FUNCTION_ACCESS correlationId=X operation=Y stateType=Z stateName=A
     *            functionClass=B functionMethod=C thread=D timestamp=E
     */
    public static void logUserFunctionAccess(String operation, String stateType, String stateName,
                                           String functionClass, String functionMethod) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("USER_FUNCTION_ACCESS correlationId={} operation={} stateType={} stateName={} " +
                "functionClass={} functionMethod={} thread={} timestamp={}",
                correlationId, operation, stateType, stateName, functionClass, functionMethod, thread, timestamp);
    }

    /**
     * Logs user function state descriptor registration for tracking state lifecycle.
     *
     * Log format: STATE_DESCRIPTOR_REGISTRATION correlationId=X stateName=Y stateType=Z
     *            functionClass=A hasDefaultValue=B hasTtl=C thread=D timestamp=E
     */
    public static void logStateDescriptorRegistration(String stateName, String stateType,
                                                    String functionClass, boolean hasDefaultValue,
                                                    boolean hasTtl) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("STATE_DESCRIPTOR_REGISTRATION correlationId={} stateName={} stateType={} " +
                "functionClass={} hasDefaultValue={} hasTtl={} thread={} timestamp={}",
                correlationId, stateName, stateType, functionClass, hasDefaultValue, hasTtl, thread, timestamp);
    }

    /**
     * Logs potentially problematic user function state access patterns.
     *
     * Log format: USER_FUNCTION_ISSUE correlationId=X issueType=Y stateName=Z details=A
     *            functionClass=B thread=C timestamp=D
     */
    public static void logUserFunctionIssue(String issueType, String stateName, String details,
                                           String functionClass) {
        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.warn("USER_FUNCTION_ISSUE correlationId={} issueType={} stateName={} details={} " +
                "functionClass={} thread={} timestamp={}",
                correlationId, issueType, stateName, details, functionClass, thread, timestamp);
    }

    /**
     * Logs state access timing information to detect long-running state operations.
     *
     * Log format: STATE_ACCESS_TIMING correlationId=X operation=Y stateName=Z duration=A
     *            functionClass=B thread=C timestamp=D
     */
    public static void logStateAccessTiming(String operation, String stateName, long durationNanos,
                                          String functionClass) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("STATE_ACCESS_TIMING correlationId={} operation={} stateName={} durationNanos={} " +
                "functionClass={} thread={} timestamp={}",
                correlationId, operation, stateName, durationNanos, functionClass, thread, timestamp);
    }

    /**
     * Logs detailed snapshot lifecycle events for debugging snapshot mechanism issues.
     *
     * Log format: SNAPSHOT_LIFECYCLE correlationId=X event=Y snapshotVersion=Z checkpointId=A
     *            mapVersion=B entryCount=C activeSnapshots=D thread=E timestamp=F
     */
    public static void logSnapshotLifecycle(String event, int snapshotVersion, long checkpointId,
                                          int mapVersion, int entryCount, int activeSnapshots) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("SNAPSHOT_LIFECYCLE correlationId={} event={} snapshotVersion={} checkpointId={} " +
                "mapVersion={} entryCount={} activeSnapshots={} thread={} timestamp={}",
                correlationId, event, snapshotVersion, checkpointId, mapVersion, entryCount,
                activeSnapshots, thread, timestamp);
    }

    /**
     * Logs snapshot iteration details for detecting missing entries during snapshot.
     *
     * Log format: SNAPSHOT_ITERATION correlationId=X phase=Y snapshotVersion=Z entriesProcessed=A
     *            currentKey=B currentNamespace=C entryVersion=D thread=E timestamp=F
     */
    public static void logSnapshotIteration(String phase, int snapshotVersion, int entriesProcessed,
                                          Object currentKey, Object currentNamespace, int entryVersion) {
        if (!LOG.isTraceEnabled()) return;

        String correlationId = createCorrelationId();
        String stateKey = createStateKey(currentKey, currentNamespace);
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("SNAPSHOT_ITERATION correlationId={} phase={} snapshotVersion={} entriesProcessed={} " +
                "stateKey={} entryVersion={} thread={} timestamp={}",
                correlationId, phase, snapshotVersion, entriesProcessed, stateKey, entryVersion, thread, timestamp);
    }

    /**
     * Logs snapshot consistency validation for detecting state corruption during snapshots.
     *
     * Log format: SNAPSHOT_VALIDATION correlationId=X validationType=Y snapshotVersion=Z
     *            expectedCount=A actualCount=B discrepancies=C thread=D timestamp=E
     */
    public static void logSnapshotValidation(String validationType, int snapshotVersion,
                                           int expectedCount, int actualCount, String discrepancies) {
        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        if (expectedCount != actualCount || !discrepancies.isEmpty()) {
            LOG.warn("SNAPSHOT_VALIDATION correlationId={} validationType={} snapshotVersion={} " +
                    "expectedCount={} actualCount={} discrepancies={} thread={} timestamp={}",
                    correlationId, validationType, snapshotVersion, expectedCount, actualCount,
                    discrepancies, thread, timestamp);
        } else if (LOG_SNAPSHOT_DETAILS) {
            LOG.debug("SNAPSHOT_VALIDATION correlationId={} validationType={} snapshotVersion={} " +
                    "expectedCount={} actualCount={} discrepancies={} thread={} timestamp={}",
                    correlationId, validationType, snapshotVersion, expectedCount, actualCount,
                    discrepancies, thread, timestamp);
        }
    }

    /**
     * Logs snapshot-state operation interactions for detecting race conditions.
     *
     * Log format: SNAPSHOT_STATE_INTERACTION correlationId=X interactionType=Y snapshotVersion=Z
     *            stateKey=A operation=B mapVersion=C entryVersion=D thread=E timestamp=F
     */
    public static void logSnapshotStateInteraction(String interactionType, int snapshotVersion,
                                                  Object key, Object namespace, String operation,
                                                  int mapVersion, int entryVersion) {
        if (!LOG.isDebugEnabled()) return;

        String correlationId = createCorrelationId();
        String stateKey = createStateKey(key, namespace);
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("SNAPSHOT_STATE_INTERACTION correlationId={} interactionType={} snapshotVersion={} " +
                "stateKey={} operation={} mapVersion={} entryVersion={} thread={} timestamp={}",
                correlationId, interactionType, snapshotVersion, stateKey, operation, mapVersion,
                entryVersion, thread, timestamp);
    }

    /**
     * Logs performance metrics for the debugging framework itself.
     *
     * Log format: DEBUG_PERFORMANCE correlationId=X metric=Y value=Z unit=A thread=B timestamp=C
     */
    public static void logPerformanceMetric(String metric, long value, String unit) {
        if (!LOG.isTraceEnabled()) return;

        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("DEBUG_PERFORMANCE correlationId={} metric={} value={} unit={} thread={} timestamp={}",
                correlationId, metric, value, unit, thread, timestamp);
    }

    /**
     * Tracks logging overhead and warns if it becomes excessive.
     */
    public static void trackLoggingOverhead(long operationDurationNanos) {
        if (!LOG.isTraceEnabled()) return;

        long currentCount = THREAD_LOG_COUNT.get() + 1;
        long currentTime = THREAD_LOG_TIME_NANOS.get() + operationDurationNanos;

        THREAD_LOG_COUNT.set(currentCount);
        THREAD_LOG_TIME_NANOS.set(currentTime);

        // Report every 1000 operations
        if (currentCount % 1000 == 0) {
            long avgTimePerLog = currentTime / currentCount;
            logPerformanceMetric("avg_log_time_per_operation", avgTimePerLog, "nanoseconds");
            logPerformanceMetric("total_log_operations", currentCount, "count");

            if (avgTimePerLog > SLOW_OPERATION_THRESHOLD_NANOS) {
                LOG.warn("DEBUG_PERFORMANCE_WARNING: Logging overhead is high. Average time per log: {} ns",
                    avgTimePerLog);
            }
        }
    }

    /**
     * Wraps logging operations with performance tracking.
     */
    private static void logWithPerformanceTracking(Runnable loggingOperation) {
        if (!LOG.isTraceEnabled()) {
            loggingOperation.run();
            return;
        }

        long startTime = System.nanoTime();
        loggingOperation.run();
        long duration = System.nanoTime() - startTime;

        trackLoggingOverhead(duration);
    }

    /**
     * Gets current performance statistics for this thread.
     *
     * Log format: DEBUG_PERFORMANCE_STATS correlationId=X totalOperations=Y totalTimeNanos=Z
     *            avgTimePerOp=A thread=B timestamp=C
     */
    public static void logCurrentPerformanceStats() {
        if (!LOG.isTraceEnabled()) return;

        long totalOps = THREAD_LOG_COUNT.get();
        long totalTime = THREAD_LOG_TIME_NANOS.get();
        long avgTime = totalOps > 0 ? totalTime / totalOps : 0;

        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("DEBUG_PERFORMANCE_STATS correlationId={} totalOperations={} totalTimeNanos={} " +
                "avgTimePerOp={} thread={} timestamp={}",
                correlationId, totalOps, totalTime, avgTime, thread, timestamp);
    }

    /**
     * Resets performance counters for the current thread.
     */
    public static void resetPerformanceCounters() {
        THREAD_LOG_COUNT.set(0L);
        THREAD_LOG_TIME_NANOS.set(0L);
    }

    /**
     * Logs configuration information about the debugging framework.
     */
    public static void logDebugConfiguration() {
        String correlationId = createCorrelationId();
        String thread = Thread.currentThread().getName();
        long timestamp = System.currentTimeMillis();

        LOG.debug("DEBUG_CONFIGURATION correlationId={} logLevel={} slowThresholdNanos={} thread={} timestamp={}",
                correlationId, LOG.getLevel(), SLOW_OPERATION_THRESHOLD_NANOS, thread, timestamp);
    }

    /**
     * Ensures configuration is logged once when debugging is first used.
     */
    private static void ensureConfigurationLogged() {
        if (!configurationLogged && LOG.isDebugEnabled()) {
            synchronized (StateDisappearanceLogger.class) {
                if (!configurationLogged) {
                    logDebugConfiguration();
                    configurationLogged = true;
                }
            }
        }
    }
}
